Dependencies for Project 'empty_LP_MSPM0G3507_nortos_keil', Target 'MSPM0G3507_Project': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (../empty.c)(0x688D30F0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MMD)
I (..\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\bsp_debugtimer.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\Hardware\bsp_systick.h)(0x68465772)
I (..\Hardware\bsp_oled.h)(0x68463120)
I (..\Hardware\bsp_siic.h)(0x68463120)
I (..\Control\balance.h)(0x6882308E)
I (..\Hardware\bsp_printf.h)(0x68463120)
I (..\Control\show.h)(0x68467F08)
I (..\Hardware\MPU6050.h)(0x684676EE)
I (..\Hardware\inv_mpu.h)(0x68463120)
I (..\Hardware\inv_mpu_dmp_motion_driver.h)(0x68463120)
I (..\Hardware\dmpKey.h)(0x68463120)
I (..\Hardware\dmpmap.h)(0x68463120)
F (../empty.syscfg)(0x68898150)()
F (startup_mspm0g350x_uvision.s)(0x6847DD9E)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x688CF504)()
F (../ti_msp_dl_config.c)(0x688CF436)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
F (..\ti\dl_vref.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_vref.o -MMD)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_uart.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_uart.o -MMD)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
F (..\ti\dl_trng.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_trng.o -MMD)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_timer.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_timer.o -MMD)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
F (..\ti\dl_spi.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_spi.o -MMD)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_rtc_common.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_rtc_common.o -MMD)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_opa.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_opa.o -MMD)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_mcan.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mcan.o -MMD)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_mathacl.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mathacl.o -MMD)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_lfss.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lfss.o -MMD)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_lcd.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lcd.o -MMD)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_keystorectl.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_keystorectl.o -MMD)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_i2c.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_i2c.o -MMD)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_flashctl.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_flashctl.o -MMD)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
F (..\ti\dl_dma.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dma.o -MMD)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_dac12.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dac12.o -MMD)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_crcp.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crcp.o -MMD)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_crc.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crc.o -MMD)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_common.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_common.o -MMD)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
F (..\ti\dl_aesadv.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aesadv.o -MMD)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_aes.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aes.o -MMD)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
F (..\ti\dl_adc12.c)(0x65B1D8C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_adc12.o -MMD)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
F (..\source\ti\driverlib\m0p\dl_interrupt.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_interrupt.o -MMD)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
F (..\source\ti\driverlib\lib\keil\m0p\mspm0g1x0x_g3x0x\driverlib.a)(0x66631CE6)()
F (..\Hardware\bsp_debugtimer.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_debugtimer.o -MMD)
I (..\Hardware\bsp_debugtimer.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
F (..\Hardware\bsp_iic.c)(0x68469DFA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_iic.o -MMD)
I (..\Hardware\bsp_siic.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\bsp_systick.h)(0x68465772)
F (..\Hardware\bsp_key.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_key.o -MMD)
I (..\Hardware\bsp_key.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
F (..\Hardware\bsp_oled.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_oled.o -MMD)
I (..\Hardware\bsp_oled.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\oledfont.h)(0x68463120)
I (..\Hardware\bsp_systick.h)(0x68465772)
F (..\Hardware\bsp_printf.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_printf.o -MMD)
I (..\Hardware\bsp_printf.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
F (..\Hardware\bsp_systick.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_systick.o -MMD)
I (..\Hardware\bsp_systick.h)(0x68465772)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
F (..\Hardware\inv_mpu.c)(0x68469528)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/inv_mpu.o -MMD)
I (..\Hardware\bsp_systick.h)(0x68465772)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\bsp_siic.h)(0x68463120)
I (..\Hardware\inv_mpu.h)(0x68463120)
I (..\Hardware\inv_mpu_dmp_motion_driver.h)(0x68463120)
I (..\Hardware\dmpKey.h)(0x68463120)
I (..\Hardware\dmpmap.h)(0x68463120)
F (..\Hardware\inv_mpu_dmp_motion_driver.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/inv_mpu_dmp_motion_driver.o -MMD)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\inv_mpu.h)(0x68463120)
I (..\Hardware\inv_mpu_dmp_motion_driver.h)(0x68463120)
I (..\Hardware\dmpKey.h)(0x68463120)
I (..\Hardware\dmpmap.h)(0x68463120)
F (..\Hardware\KF.c)(0x68463120)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/kf.o -MMD)
I (..\Hardware\KF.h)(0x68463120)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
F (..\Hardware\MPU6050.c)(0x687DE292)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/mpu6050.o -MMD)
I (..\Hardware\MPU6050.h)(0x684676EE)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\inv_mpu.h)(0x68463120)
I (..\Hardware\inv_mpu_dmp_motion_driver.h)(0x68463120)
I (..\Hardware\dmpKey.h)(0x68463120)
I (..\Hardware\dmpmap.h)(0x68463120)
I (..\Hardware\bsp_siic.h)(0x68463120)
F (..\Control\balance.c)(0x688BDD3C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/balance.o -MMD)
I (..\Control\balance.h)(0x6882308E)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Hardware\bsp_siic.h)(0x68463120)
I (..\Hardware\bsp_debugtimer.h)(0x68463120)
I (..\Hardware\bsp_key.h)(0x68463120)
I (..\Hardware\MPU6050.h)(0x684676EE)
I (..\Hardware\inv_mpu.h)(0x68463120)
I (..\Hardware\inv_mpu_dmp_motion_driver.h)(0x68463120)
I (..\Hardware\dmpKey.h)(0x68463120)
I (..\Hardware\dmpmap.h)(0x68463120)
F (..\Control\bluetooth.c)(0x68898414)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bluetooth.o -MMD)
I (..\Control\bluetooth.h)(0x68467E20)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Control\balance.h)(0x6882308E)
I (..\Hardware\bsp_printf.h)(0x68463120)
I (..\Hardware\bsp_systick.h)(0x68465772)
F (..\Control\show.c)(0x688A1BBA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../Hardware -I ../../2025CODE727 -I ../Control

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/show.o -MMD)
I (..\Control\show.h)(0x68467F08)
I (..\..\2025CODE727\ti_msp_dl_config.h)(0x688CF504)
I (..\source\ti\devices\msp\msp.h)(0x66631CE6)
I (..\source\ti\devices\DeviceFamily.h)(0x66631CE6)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66631CE6)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66961BCA)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\driverlib.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_adc12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_common.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aes.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_aesadv.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_comp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_crcp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dac12.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_dma.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_flashctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpamp.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_gpio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_i2c.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_iwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lfss.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_lcd.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mathacl.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_mcan.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_opa.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_spi.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_tamperio.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timera.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timer.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_timerg.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_trng.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_uart_main.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_vref.h)(0x66631CE6)
I (..\source\ti\driverlib\dl_wwdt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66631CE6)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x66631CE6)
I (..\Control\balance.h)(0x6882308E)
I (..\Hardware\MPU6050.h)(0x684676EE)
I (..\Hardware\inv_mpu.h)(0x68463120)
I (..\Hardware\inv_mpu_dmp_motion_driver.h)(0x68463120)
I (..\Hardware\dmpKey.h)(0x68463120)
I (..\Hardware\dmpmap.h)(0x68463120)
I (..\Hardware\bsp_oled.h)(0x68463120)
